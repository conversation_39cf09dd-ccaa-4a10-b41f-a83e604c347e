import unittest
from mean_var_std import calculate

class TestCalculate(unittest.TestCase):
    
    def test_calculate_with_valid_input(self):
        """Testa a função com entrada válida"""
        result = calculate([0, 1, 2, 3, 4, 5, 6, 7, 8])
        
        expected = {
            'mean': [[3.0, 4.0, 5.0], [1.0, 4.0, 7.0], 4.0],
            'variance': [[6.0, 6.0, 6.0], [0.6666666666666666, 0.6666666666666666, 0.6666666666666666], 6.666666666666667],
            'standard deviation': [[2.449489742783178, 2.449489742783178, 2.449489742783178], [0.816496580927726, 0.816496580927726, 0.816496580927726], 2.581988897471611],
            'max': [[6, 7, 8], [2, 5, 8], 8],
            'min': [[0, 1, 2], [0, 3, 6], 0],
            'sum': [[9, 12, 15], [3, 12, 21], 36]
        }
        
        # Verificar se as chaves estão corretas
        self.assertEqual(set(result.keys()), set(expected.keys()))
        
        # Verificar valores com tolerância para números de ponto flutuante
        for key in expected:
            if key in ['mean', 'variance', 'standard deviation']:
                # Para valores de ponto flutuante, usar assertAlmostEqual
                for i in range(len(expected[key])):
                    if isinstance(expected[key][i], list):
                        for j in range(len(expected[key][i])):
                            self.assertAlmostEqual(result[key][i][j], expected[key][i][j], places=10)
                    else:
                        self.assertAlmostEqual(result[key][i], expected[key][i], places=10)
            else:
                # Para valores inteiros, usar assertEqual
                self.assertEqual(result[key], expected[key])
    
    def test_calculate_with_invalid_input_length(self):
        """Testa se ValueError é levantado para lista com menos de 9 elementos"""
        with self.assertRaises(ValueError) as context:
            calculate([1, 2, 3, 4, 5])
        
        self.assertEqual(str(context.exception), "A lista deve conter nove números")
    
    def test_calculate_with_more_than_nine_elements(self):
        """Testa se ValueError é levantado para lista com mais de 9 elementos"""
        with self.assertRaises(ValueError) as context:
            calculate([1, 2, 3, 4, 5, 6, 7, 8, 9, 10])
        
        self.assertEqual(str(context.exception), "A lista deve conter nove números")
    
    def test_calculate_returns_lists_not_arrays(self):
        """Testa se os valores retornados são listas e não arrays numpy"""
        result = calculate([0, 1, 2, 3, 4, 5, 6, 7, 8])
        
        for key, value in result.items():
            self.assertIsInstance(value, list)
            for item in value:
                if isinstance(item, list):
                    self.assertIsInstance(item, list)
                else:
                    # Verificar se não é um array numpy
                    self.assertNotEqual(type(item).__module__, 'numpy')

if __name__ == '__main__':
    unittest.main()
