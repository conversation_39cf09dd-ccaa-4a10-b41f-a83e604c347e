# ✅ Funcionalidades Implementadas:

#### Função calculate() que:

1-> Recebe uma lista de 9 números
2-> Converte para uma matriz NumPy 3x3  
3-> Calcula estatísticas (média, variân<PERSON>, desvio padrão, máxi<PERSON>, mínimo, soma)
4-> Retorna um dicionário no formato especificado

#### Validação de entrada:

1-> Levanta ValueError com a mensagem "A lista deve conter nove números" se a lista não tiver 2-> exatamente 9 elementos

#### Cálculos corretos:

1-> axis1 (axis=0): Estatísticas das colunas
2-> axis2 (axis=1): Estatísticas das linhas
3-> flattened: Estatísticas de toda a matriz

#### Formato de saída:

1-> Todos os valores são convertidos para listas Python (não arrays NumPy)
2-> O resultado corresponde exatamente ao exemplo fornecido

# 📁 Arquivos Criados:

mean_var_std.py: Contém a função principal
main.py: Arquivo de teste para desenvolvimento
test_module.py: Testes unitários completos

# ✅ Testes Realizados:

#### ✅ Teste com o exemplo fornecido [0,1,2,3,4,5,6,7,8]

#### ✅ Teste de validação de erro para listas com menos de 9 elementos

#### ✅ Teste de validação de erro para listas com mais de 9 elementos

#### ✅ Verificação de que os valores retornados são listas Python

#### ✅ Todos os 4 testes unitários passaram
