from mean_var_std import calculate
import json

# Teste com o exemplo fornecido
test_list = [0, 1, 2, 3, 4, 5, 6, 7, 8]
result = calculate(test_list)

print("Resultado para [0, 1, 2, 3, 4, 5, 6, 7, 8]:")
print(json.dumps(result, indent=2))

print("\n" + "="*50 + "\n")

# Teste com erro (lista com menos de 9 elementos)
try:
    calculate([1, 2, 3, 4, 5])
except ValueError as e:
    print(f"Erro capturado corretamente: {e}")

print("\n" + "="*50 + "\n")

# Teste adicional com outros números
test_list2 = [1, 2, 3, 4, 5, 6, 7, 8, 9]
result2 = calculate(test_list2)

print("Resultado para [1, 2, 3, 4, 5, 6, 7, 8, 9]:")
print(json.dumps(result2, indent=2))
